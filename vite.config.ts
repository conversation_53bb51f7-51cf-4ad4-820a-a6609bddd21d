import path from 'path'
import { defineConfig, loadEnv } from 'vite'
import react from '@vitejs/plugin-react'
import UnoCSS from 'unocss/vite'
import AutoImport from 'unplugin-auto-import/vite'

// https://vite.dev/config/
export default defineConfig(({ mode }) => {
  const { VITE_APP_PROXY, VITE_APP_PROXY_PREFIX, VITE_SERVER_BASEURL, VITE_SERVER_PORT } = loadEnv(
    mode,
    path.resolve(__dirname, './env')
  )

  return {
    envDir: './env',
    plugins: [
      react(),
      UnoCSS(),
      AutoImport({
        imports: ['react'],
      }),
    ],
    resolve: {
      alias: {
        '@': path.resolve(__dirname, './src'),
      },
    },
    server: {
      host: '0.0.0.0',
      hmr: true,
      port: Number(VITE_SERVER_PORT),
      proxy: JSON.parse(VITE_APP_PROXY)
        ? {
            [VITE_APP_PROXY_PREFIX]: {
              target: VITE_SERVER_BASEURL,
              changeOrigin: true,
              secure: false,
              rewrite: (path) => path.replace(new RegExp('^' + VITE_APP_PROXY_PREFIX), ''),
            },
          }
        : undefined,
    },
  }
})
