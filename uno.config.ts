import {
  defineConfig,
  presetMini,
  presetIcons,
  presetAttributify,
  transformerDirectives,
  transformerVariantGroup,
  transformerAttributifyJsx,
} from 'unocss'

export default defineConfig({
  presets: [
    presetMini(),
    presetAttributify(),
    presetIcons({
      prefix: 'i-',
      extraProperties: {
        display: 'inline-block',
        'vertical-align': 'middle',
      },
    }),
  ],
  shortcuts: {
    card: 'p-4 rounded-md bg-white cursor-pointer',
    'flex-center': 'flex justify-center items-center',
    'flex-col-center': 'flex flex-col justify-center items-center',
  },
  transformers: [
    transformerDirectives(),
    transformerVariantGroup(),
    transformerAttributifyJsx({
      exclude: [/node_modules/],
    }),
  ],
})
