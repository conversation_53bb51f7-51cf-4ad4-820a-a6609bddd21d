import {
  defineConfig,
  presetWind4,
  presetIcons,
  presetAttributify,
  transformerDirectives,
  transformerVariantGroup,
  transformerAttributifyJsx,
} from 'unocss'

export default defineConfig({
  presets: [
    presetWind4(),
    presetAttributify(),
    presetIcons({
      prefix: 'i-',
      extraProperties: {
        display: 'inline-block',
        'vertical-align': 'middle',
      },
    }),
  ],
  transformers: [
    transformerDirectives(),
    transformerVariantGroup(),
    transformerAttributifyJsx({
      exclude: [/node_modules/],
    }),
  ],
})
