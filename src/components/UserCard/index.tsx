import React from 'react'
import { getAssetsImage } from '@/utils'
import { AvatarContainer, TagContainer, RowContainer } from './style'
import { Level } from '@/components/Level'

const UserCard: React.FunctionComponent = () => {
  return (
    <div className='p-4 rounded-md bg-white cursor-pointer'>
      <div className='flex gap-x-3.5 items-center'>
        <AvatarContainer src={getAssetsImage('avatar_man.png')} />
        <div className='space-y-2'>
          <div className='flex items-center'>
            <span className='text-base text-[#333] font-bold mr-2'>马恩源</span>
            <img className='size-4' src={getAssetsImage('sex-1.png')} alt='' />
            <span className='ml-[2px]'>24岁</span>
            <span className='text-[#666] ml-2'>18189007890</span>
          </div>
          <div className='flex gap-x-2 items-center'>
            {Array.from({ length: 2 }).map((_, index) => (
              <TagContainer key={index}>团队负责人</TagContainer>
            ))}
          </div>
          <Level level={10} />
        </div>
      </div>
      <div className='mt-2.5 flex gap-x-6 items-center justify-between'>
        <div className='flex-1 overflow-hidden space-y-2'>
          <RowContainer>
            <i className='i-octicon:organization-24 icon'></i>
            <span className='text line-clamp-1'>
              湖北益乐诚社会工作服务中心xx所属机构湖北益乐诚社会工作服务中心xx所属机构
            </span>
          </RowContainer>
          <RowContainer>
            <i className='i-weui:location-outlined icon'></i>
            <span className='text line-clamp-1'>武汉全区及周边县级市</span>
          </RowContainer>
          <RowContainer>
            <i className='i-fontisto:email icon'></i>
            <span className='text line-clamp-1'><EMAIL></span>
          </RowContainer>
        </div>

        {/* 二维码 */}
        <div className='flex flex-col items-center'>
          <img className='size-11' alt='' />
          <div className='mt-1 flex gap-x-1 items-center'>
            <i className='i-ic:baseline-wechat text-lg text-[#09BB07]'></i>
            <span className='text-sm'>咨询</span>
          </div>
        </div>
      </div>
    </div>
  )
}

export default UserCard
