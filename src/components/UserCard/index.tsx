import React from 'react'
import { getAssetsImage, getFullUrl } from '@/utils'
import { AvatarContainer, TagContainer, RowContainer } from './style'
import { Image } from 'antd'
import { Level } from '@/components/Level'

interface UserCardProps {
  data: Record<string, any>
}

const UserCard: React.FunctionComponent<UserCardProps> = (props) => {
  const { data } = props

  return (
    <div className='card transition-all-500 hover:(shadow-xl -translate-y-1)'>
      <div className='flex gap-x-3.5'>
        <AvatarContainer src={getFullUrl(data.avatar_url)} />
        <div className='space-y-2'>
          <div className='flex items-center'>
            <span className='text-base text-[#333] font-bold mr-2'>{data.realname}</span>
            <img className='size-4' src={getAssetsImage(`sex-${data.gender}.png`)} alt='' />
            <span className='text-[#666] ml-2'>{data.phone}</span>
          </div>
          {data.school_name && (
            <div className='flex gap-x-2 items-center'>
              <TagContainer className='group-hover:text-white group-hover:bg-white/20'>
                {data.school_name}
              </TagContainer>
            </div>
          )}
          <Level level={Number(data.star)} />
        </div>
      </div>
      <div className='mt-2.5 flex gap-x-6 items-center justify-between'>
        <div className='flex-1 overflow-hidden space-y-2'>
          <RowContainer>
            <i className='i-octicon:organization-24 icon group-hover:text-white'></i>
            <span className='text line-clamp-1'>{data.work_info?.organization || '--'}</span>
          </RowContainer>
          <RowContainer>
            <i className='i-tdesign:location icon'></i>
            <span className='text line-clamp-1'>{data.region_names}</span>
          </RowContainer>
          <RowContainer>
            <i className='i-clarity:email-line icon'></i>
            <span className='text line-clamp-1'>{data.email || '--'}</span>
          </RowContainer>
        </div>

        {/* 二维码 */}
        <div className='flex flex-col items-center'>
          <Image width={44} alt='' src={getFullUrl(data.wx_qrcode)} />
          <div className='mt-1 flex gap-x-1 items-center'>
            <i className='i-ic:baseline-wechat text-lg text-[#09BB07]'></i>
            <span className='text-sm'>咨询</span>
          </div>
        </div>
      </div>
    </div>
  )
}

export default UserCard
