import React from 'react'
import * as echarts from 'echarts'
import classNames from 'classnames'
import { getAssetsImage, flatToTree } from '@/utils'
import { getMarkersDataApi } from '@/service/api'
import { Button } from 'antd'
import ECharts from '@/components/Charts/ECharts'
import type { EChartsOption } from 'echarts'
import type { EChartsInstance } from '../Charts/types'

interface MapChartsProps {
  className?: string
  // 双击事件
  dblclick?: (params: echarts.ECElementEvent) => void
}

const nameMap = new Map([
  ['西藏自治区', '西藏'],
  ['广西壮族自治区', '广西'],
  ['宁夏回族自治区', '宁夏'],
  ['新疆维吾尔自治区', '新疆'],
  ['香港特别行政区', '香港'],
  ['澳门特别行政区', '澳门'],
  ['恩施土家族苗族自治州', '恩施'],
  ['神农架林区', '神农架'],
  ['内蒙古自治区', '内蒙古'],
  ['北京市', '北京'],
  ['天津市', '天津'],
  ['上海市', '上海'],
  ['重庆市', '重庆'],
])

const MAP_NAME = 'China'

const MapCharts: React.FunctionComponent<MapChartsProps> = (props) => {
  const { className, dblclick } = props

  const [codeHistory, setCodeHistory] = useState<(string | undefined)[]>([]) // 历史记录栈
  const currentCode = useRef<string | undefined>(undefined) // 当前层级代码
  const [loading, setLoading] = useState(false)
  const [mapData, setMapData] = useState<any[]>([])
  const [markerData, setMarkerData] = useState<any[]>([])

  // 是否可以返回上一级
  const canGoBack = codeHistory.length > 0

  const mapOption: EChartsOption = useMemo(() => {
    return {
      grid: {
        containLabel: true,
      },
      tooltip: {
        show: true,
      },
      geo: {
        map: MAP_NAME,
        show: true,
        roam: true,
        top: 'center',
        width: 'auto',
        height: '100%',
        label: {
          show: true,
          fontSize: 14,
          color: '#000',
          formatter: (params) => {
            const name = params.name.replace('省', '')
            const newName = nameMap.get(name)
            return newName || name
          },
        },
        itemStyle: {
          borderColor: '#666',
          areaColor: '#B6DCFF',
          borderWidth: 1,
        },
        emphasis: {
          itemStyle: {
            areaColor: '#26FFF8',
            borderColor: '#ff0000',
          },
        },
        select: {
          itemStyle: {
            areaColor: '#26FFF8',
            borderColor: '#ff0000',
          },
        },
      },
      visualMap: {
        type: 'piecewise',
        min: 0,
        splitNumber: 3,
        itemWidth: 26,
        itemHeight: 17,
        itemSymbol: 'react',
        itemGap: 14,
        pieces: [
          {
            value: 0,
            label: '导师 0 人',
            color: '#B6DCFF',
          },
          {
            value: 1,
            label: '导师 1 人',
            color: '#8AC7FF',
          },
          {
            min: 2,
            max: 10000,
            label: '导师 >1 人',
            color: '#2187FF',
          },
        ],
      },
      series: [
        {
          type: 'map',
          map: MAP_NAME,
          left: 'center',
          roam: true,
          geoIndex: 0,
          coordinateSystem: 'geo',
          universalTransition: {
            enabled: true,
          },
          data: mapData,
          z: 10,
        },
        {
          type: 'scatter',
          coordinateSystem: 'geo',
          symbol: `image://${getAssetsImage('map_火炬.png')}`,
          symbolSize: [19, 41],
          data: markerData,
        },
      ],
    }
  }, [mapData, markerData])

  const fetchMapGeoData = (code = '100000') => {
    return new Promise<any>((resolve, reject) => {
      fetch(`https://geo.datav.aliyun.com/areas_v3/bound/geojson?code=${code}_full`)
        .then((res) => {
          resolve(res.json() as any)
        })
        .catch((err) => {
          reject(err)
        })
    })
  }

  const fetchMarkersData = (region_code = '') => {
    return new Promise<any>((resolve, reject) => {
      getMarkersDataApi(region_code)
        .then((res) => {
          resolve(res)
        })
        .catch((err) => {
          reject(err)
        })
    })
  }

  const convertMarkerData = (data: any[]) => {
    return data.map((item) => ({
      name: item.full_name,
      value: item.center,
    }))
  }

  const renderMap = useCallback(
    async (code?: string, isGoingBack = false) => {
      setLoading(true)

      console.log('code', code)

      // 如果不是返回操作，需要更新历史记录
      if (!isGoingBack && currentCode.current !== code) {
        // 只有当前代码不为空且与新代码不同时，才加入历史记录
        if (currentCode.current !== undefined) {
          console.log('添加到历史记录:', currentCode.current)
          setCodeHistory((prev) => [...prev, currentCode.current])
        }
        // 更新当前代码
        currentCode.current = code
        console.log('更新当前代码为:', code)
      }

      const [geoData, markersData] = await Promise.all([
        fetchMapGeoData(code),
        fetchMarkersData(code),
      ])

      // 过滤多余的 marker 数据
      const filterMarkersData = markersData.filter((item: any) => item.region_code !== code)

      // 查找正确的 pid
      const pid = code ? filterMarkersData[0]?.pid : 0
      // 将 markersData 转为树形结构
      const treeMarkersData = flatToTree(filterMarkersData, pid)

      const data: any[] = (geoData.features as any[]).map((item: any) => {
        const adcode = String(item.properties.adcode)
        const matchedMarker = treeMarkersData.find((marker) => marker.region_code === adcode)
        const value = matchedMarker ? (matchedMarker.children?.length || 0) + 1 : 0
        return {
          ...item.properties,
          value,
        }
      })
      echarts.registerMap(MAP_NAME, geoData)
      setMapData(data)
      setMarkerData(convertMarkerData(filterMarkersData))
      setLoading(false)
    },
    [currentCode]
  )

  const beforeInit = useCallback(async () => {
    console.log('初始化地图，当前代码:', currentCode.current)
    await renderMap()
    return true
  }, [renderMap])

  const init = (instance: EChartsInstance) => {
    instance.off('dblclick')
    instance.on('dblclick', (params) => {
      if (dblclick) {
        dblclick(params)
      }
      const { adcode, level } = params.data as any
      if (level === 'district') return
      renderMap(String(adcode))
    })
  }

  // 返回上一级
  const handleGoBack = () => {
    console.log('当前历史记录:', codeHistory)
    if (codeHistory.length > 0) {
      const previousCode = codeHistory[codeHistory.length - 1]
      console.log('返回到上一级:', previousCode)
      // 从历史记录中移除最后一个
      setCodeHistory((prev) => prev.slice(0, -1))
      // 更新当前代码
      currentCode.current = previousCode
      // 渲染上一级地图
      renderMap(previousCode, true)
    } else {
      console.log('没有历史记录可以返回')
    }
  }

  return (
    <div className={classNames(className, 'size-full relative z-1')}>
      {canGoBack && (
        <div className='left-4 top-4 absolute z-9'>
          <Button type='primary' onClick={handleGoBack}>
            返回上一级 ({codeHistory.length})
          </Button>
        </div>
      )}
      <ECharts loading={loading} option={mapOption} beforeInit={beforeInit} init={init}></ECharts>
    </div>
  )
}

export default MapCharts
