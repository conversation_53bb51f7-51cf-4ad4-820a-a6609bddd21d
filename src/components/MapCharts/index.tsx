import React, { useState, useMemo, useCallback } from 'react'
import * as echarts from 'echarts'
import classNames from 'classnames'
import { getAssetsImage, flatToTree } from '@/utils'
import { getMarkersDataApi } from '@/service/api'
import ECharts from '@/components/Charts/ECharts'
import type { EChartsOption } from 'echarts'
import { useRequest } from 'ahooks'
import type { EChartsInstance } from '../Charts/types'

interface MapProps {
  className?: string
}

const nameMap = new Map([
  ['西藏自治区', '西藏'],
  ['广西壮族自治区', '广西'],
  ['宁夏回族自治区', '宁夏'],
  ['新疆维吾尔自治区', '新疆'],
  ['香港特别行政区', '香港'],
  ['澳门特别行政区', '澳门'],
  ['恩施土家族苗族自治州', '恩施'],
  ['神农架林区', '神农架'],
  ['内蒙古自治区', '内蒙古'],
  // ['北京市', '北京'],
  // ['天津市', '天津'],
  // ['上海市', '上海'],
  // ['重庆市', '重庆'],
])

const MapCharts: React.FunctionComponent<MapProps> = ({ className }) => {
  const [mapData, setMapData] = useState<any[]>([])
  const [markerData, setMarkerData] = useState<any[]>([])

  const mapOption: EChartsOption = useMemo(() => {
    console.log('markerData', markerData)
    return {
      grid: {
        containLabel: true,
      },
      geo: {
        map: 'china',
        show: true,
        roam: true,
        top: 'center',
        width: 'auto',
        height: '100%',
        label: {
          show: true,
          fontSize: 14,
          color: '#000',
          formatter: (params) => {
            const name = params.name
            const newName = nameMap.get(name)
            return newName || name
          },
        },
        itemStyle: {
          borderColor: '#A0FCFF',
          areaColor: '#B6DCFF',
          borderWidth: 1.5,
        },
        emphasis: {
          itemStyle: {
            areaColor: '#26FFF8',
            borderColor: '#ff0000',
          },
        },
        select: {
          itemStyle: {
            areaColor: '#26FFF8',
            borderColor: '#ff0000',
          },
        },
      },
      visualMap: {
        type: 'piecewise',
        min: 0,
        splitNumber: 3,
        itemWidth: 26,
        itemHeight: 17,
        itemSymbol: 'react',
        itemGap: 14,
        pieces: [
          {
            value: 0,
            label: '导师 0 人',
            color: '#B6DCFF',
          },
          {
            value: 1,
            label: '导师 1 人',
            color: '#8AC7FF',
          },
          {
            min: 2,
            max: 10000,
            label: '导师 >1 人',
            color: '#2187FF',
          },
        ],
      },
      series: [
        {
          type: 'map',
          map: 'china',
          left: 'center',
          roam: true,
          geoIndex: 0,
          coordinateSystem: 'geo',
          universalTransition: {
            enabled: true,
          },
          data: mapData,
        },
        {
          type: 'scatter',
          coordinateSystem: 'geo',
          symbol: `image://${getAssetsImage('map_火炬.png')}`,
          symbolSize: [19, 41],
          data: markerData,
        },
      ],
    }
  }, [mapData, markerData])

  const fetchMapGeoData = (code = '100000') => {
    return new Promise<any>((resolve, reject) => {
      fetch(`https://geo.datav.aliyun.com/areas_v3/bound/geojson?code=${code}_full`)
        .then((res) => {
          resolve(res.json() as any)
        })
        .catch((err) => {
          reject(err)
        })
    })
  }

  const fetchMarkersData = (region_code: string) => {
    return new Promise<any>((resolve, reject) => {
      getMarkersDataApi(region_code)
        .then((res) => {
          resolve(res)
        })
        .catch((err) => {
          reject(err)
        })
    })
  }

  const renderMap = useCallback(async () => {
    Promise.all([fetchMapGeoData(), fetchMarkersData('')])
      .then(([geoData, markersData]) => {
        const treeMarkersData = flatToTree(markersData)
        const data: any[] = (geoData.features as any[]).map((item: any) => {
          const adcode = String(item.properties.adcode)
          const matchedMarker = treeMarkersData.find((marker) => marker.region_code === adcode)
          const value = matchedMarker
           
    // const geoData = await fetchMapGeoData()
    // const markersData = await fetchMarkersData('')
    const treeMarkersData = flatToTree(markersData)
    const data: any[] = (geoData.features as any[]).map((item: any) => {
      const adcode = String(item.properties.adcode)
      const matchedMarker = treeMarkersData.find((marker) => marker.region_code === adcode)
      const value = matchedMarker
        ? matchedMarker.children?.length
          ? matchedMarker.children.length
          : 1
        : 0
      return {
        ...item.properties,
        value,
      }
    })
    echarts.registerMap('china', geoData)
    setMapData(data)
  }, [])

  const beforeInit = useCallback(async () => {
    await renderMap()
    return true
  }, [renderMap])

  const init = (instance: EChartsInstance) => {
    instance.on('click', (params) => {
      console.log('params', params)
    })
  }

  return (
    <div className={classNames('size-full relative', className)}>
      <ECharts
        loading
        className='size-full relative z-1'
        option={mapOption}
        beforeInit={beforeInit}
        init={init}
      ></ECharts>
      <img
        src={getAssetsImage('map_bg.png')}
        className='h-3/4 w-auto left-1/2 top-1/2 absolute z-0 object-cover -translate-1/2'
      />
    </div>
  )
}

export default MapCharts
