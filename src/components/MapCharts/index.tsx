import React from 'react'
import * as echarts from 'echarts'
import classNames from 'classnames'
import { useRequest } from 'ahooks'
import { getAssetsImage } from '@/utils'
import { getMarkersDataApi } from '@/service/api'
import ECharts from '@/components/Charts/ECharts'
import type { EChartsOption } from 'echarts'

interface MapProps {
  className?: string
}

const nameMap = new Map([
  ['西藏自治区', '西藏'],
  ['广西壮族自治区', '广西'],
  ['宁夏回族自治区', '宁夏'],
  ['新疆维吾尔自治区', '新疆'],
  ['香港特别行政区', '香港'],
  ['澳门特别行政区', '澳门'],
  ['恩施土家族苗族自治州', '恩施'],
  ['神农架林区', '神农架'],
  ['内蒙古自治区', '内蒙古'],
  // ['北京市', '北京'],
  // ['天津市', '天津'],
  // ['上海市', '上海'],
  // ['重庆市', '重庆'],
])

const MapCharts: React.FunctionComponent<MapProps> = ({ className }) => {
  const [mapData, setMapData] = useState<any[]>([])
  const [markerData, setMarkerData] = useState([])

  const mapOption: EChartsOption = useMemo(() => {
    return {
      grid: {
        containLabel: true,
      },
      visualMap: {
        type: 'piecewise',
        min: 0,
        splitNumber: 3,
        itemWidth: 26,
        itemHeight: 17,
        itemSymbol: 'react',
        itemGap: 14,
        pieces: [
          {
            value: 0,
            label: '导师 0 人',
            color: '#B6DCFF',
          },
          {
            value: 1,
            label: '导师 1 人',
            color: '#8AC7FF',
          },
          {
            min: 2,
            max: 10000,
            label: '导师 >1 人',
            color: '#2187FF',
          },
        ],
      },
      series: [
        {
          type: 'map',
          map: 'china',
          left: 'center',
          top: '15',
          width: '92%',
          height: 'auto',
          roam: true,
          label: {
            show: true,
            fontSize: 14,
            color: '#000',
            formatter: (params) => {
              // const name = params.name.replace(/省/, '')
              // const newName = nameMap.get(name)
              return newName || params.name
            },
          },
          itemStyle: {
            borderColor: '#A0FCFF',
          },
          emphasis: {
            itemStyle: {
              areaColor: '#26FFF8',
            },
          },
          select: {
            itemStyle: {
              areaColor: '#26FFF8',
            },
          },
          universalTransition: {
            enabled: true,
          },
          data: mapData,
        },
        {
          type: 'scatter',
          coordinateSystem: 'geo',
          symbol: `image://${getAssetsImage('map_火炬.png')}`,
          data: markerData,
        },
      ],
    }
  }, [markerData, mapData])

  const fetchMapGeoData = (code = '100000') => {
    return new Promise<any>((resolve, reject) => {
      fetch(`https://geo.datav.aliyun.com/areas_v3/bound/geojson?code=${code}_full`)
        .then((res) => {
          resolve(res.json() as any)
        })
        .catch((err) => {
          reject(err)
        })
    })
  }

  const beforeInit = async () => {
    const geoData = await fetchMapGeoData()
    const data: any[] = (geoData.features as any[]).map((item: any) => {
      const value = Math.ceil(Math.random() * 5)
      return {
        ...item.properties,
        value,
      }
    })
    setMapData(data)
    echarts.registerMap('china', geoData)
    return true
  }

  return (
    <div className={classNames('size-full relative', className)}>
      <ECharts
        className='size-full relative z-1'
        option={mapOption}
        beforeInit={beforeInit}
      ></ECharts>
      <img
        src={getAssetsImage('map_bg.png')}
        className='h-full w-auto left-1/2 top-1/2 absolute z-0 object-cover -translate-1/2'
      />
    </div>
  )
}

export default MapCharts
