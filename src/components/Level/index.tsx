import React from 'react'
import { getAssetsImage } from '@/utils'

interface LevelProps {
  level: number | string
}

export const Level: React.FunctionComponent<LevelProps> = (props) => {
  const level = Number(props.level)

  const level1Number = useMemo(() => Array.from({ length: Math.floor(level / 9) }), [level])
  const level2Number = useMemo(() => Array.from({ length: Math.floor((level % 9) / 3) }), [level])
  const level3Number = useMemo(() => Array.from({ length: (level % 9) % 3 }), [level])

  return (
    <div className='flex gap-x-1 items-center'>
      {level1Number.map((_, index) => (
        <img key={index} className='size-4' src={getAssetsImage('红星.png')} alt='' />
      ))}
      {level2Number.map((_, index) => (
        <img key={index} className='size-4' src={getAssetsImage('金星.png')} alt='' />
      ))}
      {level3Number.map((_, index) => (
        <img key={index} className='size-4' src={getAssetsImage('银星.png')} alt='' />
      ))}
    </div>
  )
}
