import type { EChartsOption, ECharts } from 'echarts'

export type EChartsInstance = ECharts

export type LoadingOptions = {
  text?: string
  textColor?: string
  fontSize?: number | string
  fontWeight?: number | string
  fontStyle?: string
  fontFamily?: string
  maskColor?: string
  showSpinner?: boolean
  color?: string
  spinnerRadius?: number
  lineWidth?: number
  zlevel?: number
}

export interface EChartsProps {
  loading?: boolean
  loadingOption?: EChartsOption
  option?: EChartsOption
  style?: React.CSSProperties
  className?: string
  autoresize?: boolean
  beforeInit?: () => boolean | Promise<boolean>
  init?: (instance: EChartsInstance) => void
}
