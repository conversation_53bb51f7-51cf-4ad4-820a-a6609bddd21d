import React from 'react'
import classNames from 'classnames'
import { useThrottleFn } from 'ahooks'
import { useECharts } from '@/hooks'
import type { EChartsProps } from './types'

const ECharts: React.FunctionComponent<EChartsProps> = React.memo((props) => {
  const {
    option,
    style,
    className,
    autoresize = true,
    loading = false,
    loadingOption,
    beforeInit,
    init,
  } = props

  const echartsRef = useRef<HTMLDivElement>(null)

  const { echartsInstance, initECharts, setOption, disposeECharts, resizeECharts } = useECharts(
    echartsRef,
    option
  )

  const { run: resizeEChartsFn } = useThrottleFn(resizeECharts, { wait: 300 })

  useEffect(() => {
    const run = async () => {
      if (beforeInit) {
        const canInit = await Promise.resolve(beforeInit())
        if (!canInit) return
      }

      initECharts()

      if (init && echartsInstance.current) {
        init(echartsInstance.current)
      }

      if (autoresize) {
        window.addEventListener('resize', resizeEChartsFn)
      }
    }

    run()
    return () => {
      disposeECharts()
      if (autoresize) {
        window.removeEventListener('resize', resizeEChartsFn)
      }
    }
  }, [])

  useEffect(() => {
    setOption(option)
  }, [option, setOption])

  useEffect(() => {
    if (loading) {
      echartsInstance.current?.showLoading(loadingOption)
    } else {
      echartsInstance.current?.hideLoading()
    }
  }, [loading, loadingOption, echartsInstance])

  return <div ref={echartsRef} className={classNames('size-full', className)} style={style} />
})

export default ECharts
