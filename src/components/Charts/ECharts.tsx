import React, { useRef, useEffect, useState } from 'react'
import classNames from 'classnames'
import { useThrottleFn } from 'ahooks'
import { useECharts } from '@/hooks'
import type { EChartsProps } from './types'

const ECharts: React.FunctionComponent<EChartsProps> = (props) => {
  const { option, style, className, autoresize = true, beforeInit, init } = props

  const [isInitialized, setIsInitialized] = useState(false)

  const echartsRef = useRef<HTMLDivElement>(null)

  const { echartsInstance, initECharts, setOption, disposeECharts, resizeECharts } =
    useECharts(echartsRef)

  const { run: resizeEChartsFn } = useThrottleFn(resizeECharts, { wait: 300 })

  useEffect(() => {
    let mounted = true

    const run = async () => {
      if (beforeInit) {
        const canInit = await Promise.resolve(beforeInit())
        if (!canInit || !mounted) return
      }

      if (!mounted) return
      initECharts()
      setIsInitialized(true)

      if (init && echartsInstance.current && mounted) {
        init(echartsInstance.current)
      }

      if (autoresize && mounted) {
        window.addEventListener('resize', resizeEChartsFn)
      }
    }

    run()
    return () => {
      mounted = false
      setIsInitialized(false)
      disposeECharts()
      if (autoresize) {
        window.removeEventListener('resize', resizeEChartsFn)
      }
    }
  }, [])

  // 选项更新 effect
  useEffect(() => {
    if (isInitialized) {
      setOption(option)
    }
  }, [option, setOption, isInitialized])

  return <div ref={echartsRef} className={classNames('size-full', className)} style={style} />
}

export default ECharts
