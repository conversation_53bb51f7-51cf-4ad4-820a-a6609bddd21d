import { getAssetsImage } from '@/utils'
import styled from 'styled-components'

export const LeftContainer = styled.div`
  position: relative;
  display: flex;
  flex-direction: column;
  flex: 1;
  height: 100%;
  z-index: 9;

  .content {
    flex: 1;
    background: linear-gradient(
      270deg,
      rgba(255, 255, 255, 0.66) -2%,
      rgba(163, 216, 254, 0.2906) 75%,
      rgba(89, 185, 255, 0.0001) 105%
    );
    overflow-y: auto;
  }
`

export const TitleContainer = styled.div`
  width: 100%;
  height: 44px;
  background: url(${getAssetsImage('title.png')}) no-repeat left top/cover;
`
