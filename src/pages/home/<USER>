import React from 'react'
import { useRequest } from 'ahooks'
import { getAssetsImage } from '@/utils'
import { getPeopleListApi } from '@/service/api'
import { LeftContainer, TitleContainer } from './style'
import UserCard from '@/components/UserCard'
import MapCharts from '@/components/MapCharts'

const starList = [
  {
    icon: getAssetsImage('银星.png'),
    year: 1,
  },
  {
    icon: getAssetsImage('金星.png'),
    year: 3,
  },
  {
    icon: getAssetsImage('红星.png'),
    year: 9,
  },
]

const Home: React.FunctionComponent = () => {
  const [code, setCode] = useState('')
  const [name, setName] = useState('全国')

  const { data: peopleList } = useRequest(() => getPeopleListApi(code), {
    refreshDeps: [code],
  })

  const handleMapDblclick = async (params: echarts.ECElementEvent) => {
    const { adcode, level, name } = params.data as any
    if (level === 'district') return
    setCode(adcode)
    setName(name)
  }

  return (
    <div className='flex size-full'>
      <LeftContainer>
        <TitleContainer></TitleContainer>
        <div className='content p-4'>
          <div className='mb-7 flex flex-wrap gap-y-3 justify-between'>
            <div className='flex gap-x-3 items-center'>
              <span className='text-base font-["MicrosoftYaHei"] font-bold'>{name}导师团队：</span>
              <span className='text-lg text-[#1281E9]'>{peopleList && peopleList.length}人</span>
            </div>
            <div className='flex gap-x-9 items-center'>
              {starList.map((item, index) => (
                <div className='flex gap-x-1.5 items-center' key={index}>
                  <img className='size-4' src={item.icon} alt='' />
                  <span>每{item.year}年</span>
                </div>
              ))}
            </div>
          </div>

          <div className='gap-3 grid grid-cols-2 lt-xl:grid-cols-1'>
            {peopleList &&
              peopleList.map((item: any, index: number) => <UserCard key={index} data={item} />)}
          </div>
        </div>
      </LeftContainer>
      <MapCharts className='w-280 lt-2xl:w-200' dblclick={handleMapDblclick} />
    </div>
  )
}

export default Home
