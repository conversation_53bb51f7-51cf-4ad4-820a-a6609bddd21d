import React from 'react'
import { getAssetsImage } from '@/utils'
import { LeftContainer, TitleContainer } from './style'
import UserCard from '@/components/UserCard'
import MapCharts from '@/components/MapCharts'

const starList = [
  {
    icon: getAssetsImage('银星.png'),
    year: 1,
  },
  {
    icon: getAssetsImage('金星.png'),
    year: 3,
  },
  {
    icon: getAssetsImage('红星.png'),
    year: 9,
  },
]

const Home: React.FunctionComponent = () => {
  return (
    <div className='flex size-full'>
      <LeftContainer>
        <TitleContainer></TitleContainer>
        <div className='content p-4'>
          <div className='mb-7 flex justify-between'>
            <div className='flex gap-x-3 items-center'>
              <span className='text-base font-["MicrosoftYaHei"] font-bold'>湖北省导师团队：</span>
              <span className='text-lg text-[#1281E9]'>14人</span>
            </div>
            <div className='flex gap-x-9 items-center'>
              {starList.map((item, index) => (
                <div className='flex gap-x-1.5 items-center' key={index}>
                  <img className='size-4' src={item.icon} alt='' />
                  <span>每{item.year}年</span>
                </div>
              ))}
            </div>
          </div>

          <div className='gap-3 grid grid-cols-2 lt-xl:grid-cols-1'>
            {Array.from({ length: 10 }).map((_, index) => (
              <UserCard key={index} />
            ))}
          </div>
        </div>
      </LeftContainer>
      <MapCharts className='w-280 lt-2xl:w-200' />
    </div>
  )
}

export default Home
