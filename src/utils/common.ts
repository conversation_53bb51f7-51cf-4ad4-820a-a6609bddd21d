import type { FlatData, TreeData } from '@/utils/types'

/**
 * 获取assets/images下的图片
 * @param name 图片名称
 * @returns 图片路径
 */
export const getAssetsImage = (name: string) => {
  return new URL(`/src/assets/images/${name}`, import.meta.url).href
}

export const getFullUrl = (url: string) => {
  return import.meta.env.VITE_SERVER_BASEURL + url
}

/**
 * 一维数组转树形结构
 * @param data 一维数组
 * @param pid 父级id
 * @returns 树形结构
 */
export const flatToTree = <T extends FlatData>(data: T, pid = 0): TreeData => {
  return data
    .filter((item) => item.pid === pid)
    .map((item) => ({ ...item, children: flatToTree(data, item.id) }))
}
