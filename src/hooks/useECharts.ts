import React from 'react'
import * as echarts from 'echarts'
import type { ECharts, EChartsOption } from 'echarts'

export const useECharts = (el: React.RefObject<HTMLDivElement | null>, option?: EChartsOption) => {
  const echartsInstance = useRef<ECharts | null>(null)

  const initECharts = () => {
    if (!el.current || echartsInstance.current) return
    echartsInstance.current = echarts.init(el.current)
    setOption(option)
  }

  const setOption = (option?: EChartsOption) => {
    if (echartsInstance.current && option) {
      echartsInstance.current.setOption(option, true)
    }
  }

  const resizeECharts = () => {
    if (echartsInstance.current) {
      echartsInstance.current.resize()
    }
  }

  const disposeECharts = () => {
    if (echartsInstance.current) {
      echartsInstance.current.dispose()
      echartsInstance.current = null
    }
  }

  return {
    echartsInstance,
    initECharts,
    setOption,
    resizeECharts,
    disposeECharts,
  }
}
