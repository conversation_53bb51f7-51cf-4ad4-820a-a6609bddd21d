import React, { useRef, useCallback } from 'react'
import * as echarts from 'echarts'
import type { ECharts } from 'echarts'
import type { EChartsCoreOption } from 'echarts/core'

export const useECharts = (el: React.RefObject<HTMLDivElement | null>) => {
  const echartsInstance = useRef<ECharts | null>(null)

  const setOption = useCallback((option?: EChartsCoreOption) => {
    if (echartsInstance.current && option) {
      echartsInstance.current.setOption(option, true)
    }
  }, [])

  const initECharts = useCallback(() => {
    if (!el.current || echartsInstance.current) return
    echartsInstance.current = echarts.init(el.current)
  }, [el])

  const resizeECharts = useCallback(() => {
    if (echartsInstance.current) {
      echartsInstance.current.resize()
    }
  }, [])

  const disposeECharts = useCallback(() => {
    if (echartsInstance.current) {
      echartsInstance.current.dispose()
      echartsInstance.current = null
    }
  }, [])

  return {
    echartsInstance,
    initECharts,
    setOption,
    resizeECharts,
    disposeECharts,
  }
}
