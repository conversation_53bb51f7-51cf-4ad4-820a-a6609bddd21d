import { get, post } from '../request'
import type { LoginParams, LoginResponse, MarkersDataResponse } from './types'

/**
 * @description 登录
 * @returns
 */
export const loginApi = (data: LoginParams) => post<LoginResponse>('/api/auth/login', data)

/**
 * @description 获取标记数据
 * @returns
 */
export const getMarkersDataApi = (region_code: string) => {
  return get<MarkersDataResponse>(`/api/sunshine.Guide/markers?region_code=${region_code}`)
}

/**
 * @description 获取导师列表
 * @param region_code
 * @returns
 */
export const getPeopleListApi = (region_code: string) => {
  return get<any>(`/api/sunshine.Guide/guides?region_code=${region_code}`)
}
