import { loginApi } from '@/service/api'
import { removeToken, setToken } from '@/utils'

let isRefreshing = false // 当前是否在请求刷新 Token
let requestQueue: any[] = [] // 将在请求刷新 Token 中的请求暂存起来，等刷新 Token 后再重新请求

// 执行暂存起来的请求
const executeQueue = (error: any) => {
  requestQueue.forEach((promise) => {
    if (error) {
      promise.reject(error)
    } else {
      promise.resolve()
    }
  })

  requestQueue = []
}

// 刷新 Token 请求处理，参数为刷新成功后的回调函数
const refreshToken = () => {
  // 如果当前是在请求刷新 Token 中，则将期间的请求暂存起来
  if (isRefreshing) {
    return new Promise((resolve, reject) => {
      requestQueue.push({ resolve, reject })
    })
  }

  isRefreshing = true

  return new Promise((resolve, reject) => {
    loginApi({
      open_id: 'o4J8P7XSoVnjKCbR_q-qL7-19sr4',
      platform: 'pc',
    })
      .then((res) => {
        setToken(res.token)
        resolve(null)
        executeQueue(null)
      })
      .catch((err) => {
        removeToken()
        reject(err)
        executeQueue(err || new Error('Refresh token error'))
      })
      .finally(() => {
        isRefreshing = false
      })
  })
}

export default refreshToken
