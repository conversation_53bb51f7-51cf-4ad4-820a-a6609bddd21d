import axios from 'axios'
import { getToken } from '@/utils'
import { message } from 'antd'
import refreshToken from './refresh'
import type { AxiosRequestConfig } from 'axios'
import type { RequestResponse } from './types'

const isProxy = JSON.parse(import.meta.env.VITE_APP_PROXY)
const baseURL = isProxy
  ? import.meta.env.VITE_APP_PROXY_PREFIX
  : import.meta.env.VITE_SERVER_BASEURL

const request = axios.create({
  baseURL,
  timeout: 10000,
  headers: {
    server: 1,
  },
})

request.interceptors.request.use(
  (config) => {
    const token = getToken()
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

request.interceptors.response.use(
  (response) => {
    const { code, data, msg } = response.data as RequestResponse
    if (code === 1) {
      return data
    } else if (code === 401) {
      // 刷新 Token
      return refreshToken().then(() => request(response.config))
    } else {
      message.error(msg)
      return Promise.reject(msg)
    }
  },
  (error) => {
    return Promise.reject(error)
  }
)

export const get = <T = any>(
  url: string,
  params?: any,
  config?: AxiosRequestConfig
): Promise<T> => {
  return request.get(url, { ...params, ...config })
}

export const post = <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> => {
  return request.post(url, { ...data, ...config })
}

export default request
