{"name": "xtwy-map", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "format": "prettier --write .", "preview": "vite preview"}, "dependencies": {"@ant-design/icons": "~5.6.1", "@unocss/reset": "^66.4.2", "ahooks": "^3.9.0", "antd": "^5.26.7", "axios": "^1.11.0", "classnames": "^2.5.1", "echarts": "^6.0.0", "immer": "^10.1.1", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router": "^7.7.1", "styled-components": "^6.1.19"}, "devDependencies": {"@eslint/js": "^9.30.1", "@iconify/json": "^2.2.369", "@types/node": "^24.2.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@unocss/eslint-config": "^66.4.2", "@unocss/preset-rem-to-px": "^66.4.2", "@vitejs/plugin-react": "^4.6.0", "eslint": "^9.30.1", "eslint-config-prettier": "^10.1.8", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "postcss-px-to-viewport-8-plugin": "^1.2.5", "prettier": "^3.6.2", "typescript": "~5.8.3", "typescript-eslint": "^8.35.1", "unocss": "^66.4.2", "unplugin-auto-import": "^20.0.0", "vite": "^7.0.4"}}